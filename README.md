# TONI V3 AI Backend

Tony (T.O.N.I.) is a multimodal AI experience coordinator designed to help app users engage more in their community, make friends, and enjoy life.

## Features

- **Personalized Onboarding**: Interactive voice/text interviews to gather user preferences
- **AI Chat Assistant**: One-on-one conversations for activity suggestions and social guidance
- **Smart Introductions**: AI-facilitated user matching based on shared interests
- **Group Coordination**: Multi-user event planning and activity coordination
- **Voice Integration**: Real-time voice conversations using OpenAI's Realtime API
- **Calendar Integration**: Google Calendar sync for availability and event management

## Technology Stack

- **Backend**: FastAPI (Python 3.9+)
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Cache**: Redis for session management
- **AI**: OpenAI GPT-4 and Realtime Voice API
- **Authentication**: OAuth2 with Google/Apple Sign-In
- **Deployment**: Docker-ready with CI/CD support

## Quick Start

### Prerequisites

- Python 3.9 or higher
- PostgreSQL database
- Redis instance
- OpenAI API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd toni-v3-backend
```

2. Set up development environment:
```bash
make setup
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Install dependencies:
```bash
make install-dev
```

5. Run database migrations:
```bash
make db-upgrade
```

6. Start the development server:
```bash
make dev
```

The API will be available at `http://localhost:8000`

## Development

### Available Commands

```bash
make help          # Show all available commands
make install       # Install production dependencies
make install-dev   # Install development dependencies
make test          # Run tests
make lint          # Run code linting
make format        # Format code
make run           # Run production server
make dev           # Run development server with reload
```

### Project Structure

```
toni-v3-backend/
├── app/
│   ├── api/v1/          # API endpoints
│   ├── core/            # Core configuration and utilities
│   ├── models/          # Database models
│   ├── services/        # Business logic
│   └── utils/           # Helper functions
├── tests/               # Test suite
├── alembic/            # Database migrations
├── main.py             # Application entry point
└── requirements.txt    # Dependencies
```

## API Documentation

When running in development mode, API documentation is available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Testing

Run the test suite:
```bash
make test
```

Run tests with coverage:
```bash
make test-cov
```

## Deployment

### Docker

Build and run with Docker:
```bash
make docker-build
make docker-run
```

### Production

1. Set environment to production in `.env`
2. Configure production database and Redis
3. Run with gunicorn:
```bash
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
