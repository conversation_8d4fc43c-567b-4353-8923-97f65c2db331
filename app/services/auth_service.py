"""
TONI V3 AI Backend - Authentication Service

Business logic for user authentication, registration, and OAuth flows.
"""

from typing import Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime
import uuid
import logging

from app.models.user import User, UserProfile
from app.core.auth import token_manager, password_manager, google_oauth
from app.core.exceptions import (
    UserNotFoundError,
    UserAlreadyExistsError,
    AuthenticationError,
    ErrorCodes
)
from app.core.logging import get_logger

logger = get_logger(__name__)


class AuthService:
    """Authentication service for user management"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(
        self,
        email: str,
        name: str,
        auth_provider: str,
        auth_provider_id: Optional[str] = None,
        **kwargs
    ) -> User:
        """
        Create a new user
        
        Args:
            email: User email
            name: User name
            auth_provider: Authentication provider (google, apple, email)
            auth_provider_id: External provider ID
            **kwargs: Additional user data
        
        Returns:
            User: Created user
        
        Raises:
            UserAlreadyExistsError: If user already exists
        """
        try:
            # Check if user already exists
            existing_user = await self.get_user_by_email(email)
            if existing_user:
                raise UserAlreadyExistsError(
                    f"User with email {email} already exists",
                    ErrorCodes.USER_ALREADY_EXISTS
                )
            
            # Create new user
            user = User(
                email=email,
                name=name,
                auth_provider=auth_provider,
                auth_provider_id=auth_provider_id,
                is_verified=auth_provider in ["google", "apple"],  # OAuth users are pre-verified
                **kwargs
            )
            
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            
            # Create user profile
            profile = UserProfile(user_id=user.id)
            self.db.add(profile)
            await self.db.commit()
            
            logger.info(f"Created new user: {user.id} ({email})")
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create user {email}: {e}")
            raise
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email
        
        Args:
            email: User email
        
        Returns:
            User or None: User if found
        """
        try:
            result = await self.db.execute(
                select(User).where(User.email == email, User.is_active == True)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get user by email {email}: {e}")
            return None
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """
        Get user by ID
        
        Args:
            user_id: User ID
        
        Returns:
            User or None: User if found
        """
        try:
            result = await self.db.execute(
                select(User).where(User.id == user_id, User.is_active == True)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get user by ID {user_id}: {e}")
            return None
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password
        
        Args:
            email: User email
            password: User password
        
        Returns:
            User or None: User if authentication successful
        """
        user = await self.get_user_by_email(email)
        if not user:
            return None
        
        # For OAuth users, password authentication is not supported
        if user.auth_provider != "email":
            return None
        
        # TODO: Implement password storage and verification for email users
        # For now, OAuth-only authentication is supported
        return None
    
    async def create_user_tokens(self, user: User) -> Dict[str, str]:
        """
        Create access and refresh tokens for user
        
        Args:
            user: User object
        
        Returns:
            dict: Access and refresh tokens
        """
        # Update last login
        user.last_login = datetime.utcnow()
        await self.db.commit()
        
        # Create token payload
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "name": user.name,
            "is_verified": user.is_verified,
            "is_admin": False,  # TODO: Implement admin role system
        }
        
        # Generate tokens
        access_token = token_manager.create_access_token(token_data)
        refresh_token = token_manager.create_refresh_token({"sub": str(user.id)})
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """
        Refresh access token using refresh token
        
        Args:
            refresh_token: Refresh token
        
        Returns:
            dict: New access token
        
        Raises:
            AuthenticationError: If refresh token is invalid
        """
        try:
            # Verify refresh token
            payload = token_manager.verify_token(refresh_token, "refresh")
            user_id = payload.get("sub")
            
            if not user_id:
                raise AuthenticationError("Invalid refresh token")
            
            # Get user
            user = await self.get_user_by_id(user_id)
            if not user:
                raise AuthenticationError("User not found")
            
            # Create new access token
            token_data = {
                "sub": str(user.id),
                "email": user.email,
                "name": user.name,
                "is_verified": user.is_verified,
                "is_admin": False,
            }
            
            access_token = token_manager.create_access_token(token_data)
            
            return {
                "access_token": access_token,
                "token_type": "bearer"
            }
            
        except Exception as e:
            logger.error(f"Failed to refresh token: {e}")
            raise AuthenticationError("Invalid refresh token")
    
    async def handle_google_oauth(self, code: str, state: Optional[str] = None) -> Tuple[User, Dict[str, str]]:
        """
        Handle Google OAuth callback
        
        Args:
            code: Authorization code from Google
            state: State parameter for CSRF protection
        
        Returns:
            tuple: User and tokens
        
        Raises:
            AuthenticationError: If OAuth flow fails
        """
        try:
            # Exchange code for token
            token_response = await google_oauth.exchange_code_for_token(code)
            access_token = token_response.get("access_token")
            
            if not access_token:
                raise AuthenticationError("Failed to get access token from Google")
            
            # Get user info from Google
            user_info = await google_oauth.get_user_info(access_token)
            
            email = user_info.get("email")
            name = user_info.get("name")
            google_id = user_info.get("id")
            
            if not email or not name:
                raise AuthenticationError("Incomplete user information from Google")
            
            # Check if user exists
            user = await self.get_user_by_email(email)
            
            if user:
                # Update Google ID if not set
                if not user.auth_provider_id and user.auth_provider == "google":
                    user.auth_provider_id = google_id
                    await self.db.commit()
            else:
                # Create new user
                user = await self.create_user(
                    email=email,
                    name=name,
                    auth_provider="google",
                    auth_provider_id=google_id
                )
            
            # Create tokens
            tokens = await self.create_user_tokens(user)
            
            logger.info(f"Google OAuth successful for user: {user.id}")
            return user, tokens
            
        except Exception as e:
            logger.error(f"Google OAuth failed: {e}")
            raise AuthenticationError(f"OAuth authentication failed: {str(e)}")
    
    async def deactivate_user(self, user_id: str) -> bool:
        """
        Deactivate user account
        
        Args:
            user_id: User ID
        
        Returns:
            bool: True if successful
        """
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.is_active = False
            await self.db.commit()
            
            logger.info(f"Deactivated user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to deactivate user {user_id}: {e}")
            return False
