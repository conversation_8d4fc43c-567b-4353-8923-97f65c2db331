"""
TONI V3 AI Backend - Chat Service

Business logic for Tony AI chat functionality and conversation management.
"""

from typing import Dict, Any, List, Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
from sqlalchemy.orm import selectinload
from datetime import datetime
import json
import logging

from app.models.chat import Conversation, Message
from app.models.user import User, UserProfile
from app.core.openai_client import get_chat_service, get_moderation_service
from app.core.exceptions import (
    ChatError,
    UserNotFoundError,
    ValidationError,
    ContentModerationError,
    ErrorCodes
)
from app.core.logging import get_logger

logger = get_logger(__name__)


class TonyChatService:
    """Tony AI chat service for conversations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.chat_service = get_chat_service()
        self.moderation_service = get_moderation_service()
    
    async def create_conversation(
        self,
        user_id: str,
        conversation_type: str = "chat",
        title: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Conversation:
        """
        Create a new conversation
        
        Args:
            user_id: User ID
            conversation_type: Type of conversation (chat, onboarding, introduction, group)
            title: Optional conversation title
            context: Optional conversation context
        
        Returns:
            Conversation: Created conversation
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            # Verify user exists
            result = await self.db.execute(
                select(User).where(User.id == user_id, User.is_active == True)
            )
            user = result.scalar_one_or_none()
            
            if not user:
                raise UserNotFoundError(f"User {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            # Create conversation
            conversation = Conversation(
                user_id=user_id,
                conversation_type=conversation_type,
                title=title,
                context=context or {},
                is_active=True
            )
            
            self.db.add(conversation)
            await self.db.commit()
            await self.db.refresh(conversation)
            
            logger.info(f"Created conversation: {conversation.id} for user: {user_id}")
            return conversation
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to create conversation for user {user_id}: {e}")
            await self.db.rollback()
            raise ChatError(f"Failed to create conversation: {str(e)}")
    
    async def get_conversation(self, conversation_id: str, user_id: str) -> Optional[Conversation]:
        """
        Get conversation by ID for a specific user
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID
        
        Returns:
            Conversation or None: Conversation if found and belongs to user
        """
        try:
            result = await self.db.execute(
                select(Conversation)
                .options(selectinload(Conversation.messages))
                .where(
                    Conversation.id == conversation_id,
                    Conversation.user_id == user_id,
                    Conversation.is_active == True
                )
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id}: {e}")
            return None
    
    async def get_user_conversations(
        self,
        user_id: str,
        conversation_type: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Conversation]:
        """
        Get user's conversations
        
        Args:
            user_id: User ID
            conversation_type: Optional filter by conversation type
            limit: Maximum number of conversations
            offset: Number of conversations to skip
        
        Returns:
            list: List of conversations
        """
        try:
            query = select(Conversation).where(
                Conversation.user_id == user_id,
                Conversation.is_active == True
            )
            
            if conversation_type:
                query = query.where(Conversation.conversation_type == conversation_type)
            
            query = query.order_by(desc(Conversation.last_message_at)).limit(limit).offset(offset)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Failed to get conversations for user {user_id}: {e}")
            return []
    
    async def send_message(
        self,
        conversation_id: str,
        user_id: str,
        content: str,
        message_type: str = "text"
    ) -> Dict[str, Any]:
        """
        Send a message and get Tony's response
        
        Args:
            conversation_id: Conversation ID
            user_id: User ID
            content: Message content
            message_type: Type of message (text, audio, etc.)
        
        Returns:
            dict: User message and Tony's response
        
        Raises:
            ChatError: If message sending fails
            ContentModerationError: If content violates policies
        """
        try:
            # Get conversation
            conversation = await self.get_conversation(conversation_id, user_id)
            if not conversation:
                raise ChatError("Conversation not found", ErrorCodes.RESOURCE_NOT_FOUND)
            
            # Moderate content
            moderation_result = await self.moderation_service.moderate_content(content)
            if moderation_result["flagged"]:
                raise ContentModerationError(
                    "Message content violates content policy",
                    ErrorCodes.CONTENT_MODERATION_VIOLATION,
                    details=moderation_result
                )
            
            # Save user message
            user_message = Message(
                conversation_id=conversation_id,
                content=content,
                message_type=message_type,
                sender_type="user",
                sender_id=user_id
            )
            
            self.db.add(user_message)
            await self.db.commit()
            await self.db.refresh(user_message)
            
            # Get Tony's response
            tony_response = await self._generate_tony_response(conversation, content)
            
            # Save Tony's message
            tony_message = Message(
                conversation_id=conversation_id,
                content=tony_response["content"],
                message_type="text",
                sender_type="tony",
                model_used=tony_response.get("model"),
                tokens_used=tony_response.get("tokens_used"),
                response_time=tony_response.get("response_time")
            )
            
            self.db.add(tony_message)
            
            # Update conversation
            conversation.last_message_at = datetime.utcnow()
            conversation.message_count += 2  # User message + Tony's response
            
            await self.db.commit()
            await self.db.refresh(tony_message)
            
            logger.info(f"Message exchange completed in conversation: {conversation_id}")
            
            return {
                "user_message": {
                    "id": str(user_message.id),
                    "content": user_message.content,
                    "message_type": user_message.message_type,
                    "sender_type": user_message.sender_type,
                    "created_at": user_message.created_at.isoformat()
                },
                "tony_response": {
                    "id": str(tony_message.id),
                    "content": tony_message.content,
                    "message_type": tony_message.message_type,
                    "sender_type": tony_message.sender_type,
                    "created_at": tony_message.created_at.isoformat(),
                    "model_used": tony_message.model_used,
                    "tokens_used": tony_message.tokens_used
                }
            }
            
        except (ChatError, ContentModerationError):
            raise
        except Exception as e:
            logger.error(f"Failed to send message in conversation {conversation_id}: {e}")
            await self.db.rollback()
            raise ChatError(f"Failed to send message: {str(e)}")
    
    async def _generate_tony_response(
        self,
        conversation: Conversation,
        user_message: str
    ) -> Dict[str, Any]:
        """
        Generate Tony's response using OpenAI
        
        Args:
            conversation: Conversation object
            user_message: User's message content
        
        Returns:
            dict: Tony's response data
        """
        try:
            # Get conversation history
            messages = await self._build_conversation_context(conversation)
            
            # Add user message
            messages.append({
                "role": "user",
                "content": user_message
            })
            
            # Generate response
            start_time = datetime.utcnow()
            response = await self.chat_service.create_completion(
                messages=messages,
                temperature=0.7,
                max_tokens=500
            )
            end_time = datetime.utcnow()
            
            response_time = int((end_time - start_time).total_seconds() * 1000)
            
            return {
                "content": response["content"],
                "model": response["model"],
                "tokens_used": response["usage"]["total_tokens"] if response["usage"] else None,
                "response_time": response_time
            }
            
        except Exception as e:
            logger.error(f"Failed to generate Tony response: {e}")
            # Return fallback response
            return {
                "content": "I'm sorry, I'm having trouble responding right now. Please try again in a moment.",
                "model": "fallback",
                "tokens_used": None,
                "response_time": 0
            }
    
    async def _build_conversation_context(self, conversation: Conversation) -> List[Dict[str, str]]:
        """
        Build conversation context for OpenAI
        
        Args:
            conversation: Conversation object
        
        Returns:
            list: List of message objects for OpenAI
        """
        try:
            # Get user profile for context
            result = await self.db.execute(
                select(UserProfile).where(UserProfile.user_id == conversation.user_id)
            )
            profile = result.scalar_one_or_none()
            
            # Build system prompt
            system_prompt = self._build_system_prompt(conversation, profile)
            
            messages = [{"role": "system", "content": system_prompt}]
            
            # Get recent conversation history (last 20 messages)
            result = await self.db.execute(
                select(Message)
                .where(
                    Message.conversation_id == conversation.id,
                    Message.is_deleted == False
                )
                .order_by(desc(Message.created_at))
                .limit(20)
            )
            recent_messages = result.scalars().all()
            
            # Add messages in chronological order
            for message in reversed(recent_messages):
                role = "user" if message.sender_type == "user" else "assistant"
                messages.append({
                    "role": role,
                    "content": message.content
                })
            
            return messages
            
        except Exception as e:
            logger.error(f"Failed to build conversation context: {e}")
            # Return minimal context
            return [{"role": "system", "content": self._get_default_system_prompt()}]
    
    def _build_system_prompt(
        self,
        conversation: Conversation,
        profile: Optional[UserProfile] = None
    ) -> str:
        """
        Build system prompt for Tony based on conversation type and user profile
        
        Args:
            conversation: Conversation object
            profile: User profile object
        
        Returns:
            str: System prompt
        """
        base_prompt = """You are Tony (T.O.N.I.), a friendly AI assistant designed to help people engage more in their community, make friends, and enjoy life. You are warm, encouraging, and genuinely interested in helping users connect with others and discover new experiences.

Your core personality traits:
- Warm and approachable, like a friendly neighbor
- Enthusiastic about community and social connections
- Knowledgeable about local events and activities
- Supportive and encouraging without being pushy
- Respectful of boundaries and privacy preferences

Your main goals:
- Help users discover their interests and preferences
- Suggest relevant community events and activities
- Facilitate meaningful connections between users
- Encourage social engagement while respecting comfort levels"""
        
        # Add conversation-specific context
        if conversation.conversation_type == "onboarding":
            base_prompt += "\n\nYou are currently conducting an onboarding conversation to learn about the user's interests, personality, and social preferences. Ask engaging questions and be genuinely curious about their responses."
        
        elif conversation.conversation_type == "chat":
            base_prompt += "\n\nYou are having a casual conversation with the user. Be helpful, friendly, and look for opportunities to suggest relevant activities or connections."
        
        # Add user profile context if available
        if profile and profile.interests:
            interests_str = ", ".join(profile.interests)
            base_prompt += f"\n\nThe user has expressed interest in: {interests_str}. Use this information to personalize your responses and suggestions."
        
        if profile and profile.location:
            base_prompt += f"\n\nThe user is located in: {profile.location}. Consider this when suggesting local events or activities."
        
        return base_prompt
    
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt when context building fails"""
        return """You are Tony (T.O.N.I.), a friendly AI assistant designed to help people engage more in their community, make friends, and enjoy life. You are warm, encouraging, and genuinely interested in helping users connect with others and discover new experiences."""
