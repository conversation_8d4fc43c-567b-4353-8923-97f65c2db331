"""
TONI V3 AI Backend - User Service

Business logic for user profile management and user-related operations.
"""

from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from datetime import datetime
import logging

from app.models.user import User, UserProfile
from app.core.exceptions import (
    UserNotFoundError,
    ValidationError,
    ErrorCodes
)
from app.core.logging import get_logger

logger = get_logger(__name__)


class UserService:
    """User management service"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user profile with all related data
        
        Args:
            user_id: User ID
        
        Returns:
            dict: User profile data
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            # Get user with profile
            result = await self.db.execute(
                select(User)
                .options(selectinload(User.profile))
                .where(User.id == user_id, User.is_active == True)
            )
            user = result.scalar_one_or_none()
            
            if not user:
                raise UserNotFoundError(f"User {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            profile = user.profile
            if not profile:
                # Create profile if it doesn't exist
                profile = UserProfile(user_id=user.id)
                self.db.add(profile)
                await self.db.commit()
                await self.db.refresh(profile)
            
            return {
                "id": str(user.id),
                "email": user.email,
                "name": user.name,
                "auth_provider": user.auth_provider,
                "is_verified": user.is_verified,
                "created_at": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "profile": {
                    "bio": profile.bio,
                    "location": profile.location,
                    "age_range": profile.age_range,
                    "interests": profile.interests,
                    "personality_traits": profile.personality_traits,
                    "social_preferences": profile.social_preferences,
                    "availability": profile.availability,
                    "onboarding_completed": profile.onboarding_completed,
                    "privacy_settings": profile.privacy_settings,
                    "introduction_preferences": profile.introduction_preferences,
                    "created_at": profile.created_at.isoformat(),
                    "updated_at": profile.updated_at.isoformat() if profile.updated_at else None
                }
            }
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to get user profile {user_id}: {e}")
            raise
    
    async def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update user profile
        
        Args:
            user_id: User ID
            profile_data: Profile data to update
        
        Returns:
            dict: Updated profile data
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            # Get user profile
            result = await self.db.execute(
                select(UserProfile).where(UserProfile.user_id == user_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                raise UserNotFoundError(f"User profile {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            # Update allowed fields
            allowed_fields = [
                'bio', 'location', 'age_range', 'interests', 'personality_traits',
                'social_preferences', 'availability', 'privacy_settings',
                'introduction_preferences'
            ]
            
            for field in allowed_fields:
                if field in profile_data:
                    setattr(profile, field, profile_data[field])
            
            profile.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(profile)
            
            logger.info(f"Updated user profile: {user_id}")
            
            return {
                "bio": profile.bio,
                "location": profile.location,
                "age_range": profile.age_range,
                "interests": profile.interests,
                "personality_traits": profile.personality_traits,
                "social_preferences": profile.social_preferences,
                "availability": profile.availability,
                "privacy_settings": profile.privacy_settings,
                "introduction_preferences": profile.introduction_preferences,
                "updated_at": profile.updated_at.isoformat()
            }
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to update user profile {user_id}: {e}")
            await self.db.rollback()
            raise
    
    async def update_user_interests(self, user_id: str, interests: List[str]) -> List[str]:
        """
        Update user interests
        
        Args:
            user_id: User ID
            interests: List of interest strings
        
        Returns:
            list: Updated interests
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            result = await self.db.execute(
                select(UserProfile).where(UserProfile.user_id == user_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                raise UserNotFoundError(f"User profile {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            # Validate and clean interests
            cleaned_interests = []
            for interest in interests:
                if isinstance(interest, str) and interest.strip():
                    cleaned_interests.append(interest.strip().lower())
            
            # Remove duplicates while preserving order
            unique_interests = []
            seen = set()
            for interest in cleaned_interests:
                if interest not in seen:
                    unique_interests.append(interest)
                    seen.add(interest)
            
            profile.interests = unique_interests
            profile.updated_at = datetime.utcnow()
            await self.db.commit()
            
            logger.info(f"Updated user interests: {user_id}")
            return unique_interests
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to update user interests {user_id}: {e}")
            await self.db.rollback()
            raise
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        Get user preferences and settings
        
        Args:
            user_id: User ID
        
        Returns:
            dict: User preferences
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            result = await self.db.execute(
                select(UserProfile).where(UserProfile.user_id == user_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                raise UserNotFoundError(f"User profile {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            return {
                "social_preferences": profile.social_preferences or {},
                "privacy_settings": profile.privacy_settings or {},
                "introduction_preferences": profile.introduction_preferences or {},
                "availability": profile.availability or {}
            }
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to get user preferences {user_id}: {e}")
            raise
    
    async def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update user preferences and settings
        
        Args:
            user_id: User ID
            preferences: Preferences to update
        
        Returns:
            dict: Updated preferences
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            result = await self.db.execute(
                select(UserProfile).where(UserProfile.user_id == user_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                raise UserNotFoundError(f"User profile {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            # Update preference categories
            if "social_preferences" in preferences:
                profile.social_preferences = preferences["social_preferences"]
            
            if "privacy_settings" in preferences:
                profile.privacy_settings = preferences["privacy_settings"]
            
            if "introduction_preferences" in preferences:
                profile.introduction_preferences = preferences["introduction_preferences"]
            
            if "availability" in preferences:
                profile.availability = preferences["availability"]
            
            profile.updated_at = datetime.utcnow()
            await self.db.commit()
            
            logger.info(f"Updated user preferences: {user_id}")
            
            return {
                "social_preferences": profile.social_preferences or {},
                "privacy_settings": profile.privacy_settings or {},
                "introduction_preferences": profile.introduction_preferences or {},
                "availability": profile.availability or {}
            }
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to update user preferences {user_id}: {e}")
            await self.db.rollback()
            raise
    
    async def get_onboarding_status(self, user_id: str) -> Dict[str, Any]:
        """
        Get user onboarding status
        
        Args:
            user_id: User ID
        
        Returns:
            dict: Onboarding status
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            result = await self.db.execute(
                select(UserProfile).where(UserProfile.user_id == user_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                raise UserNotFoundError(f"User profile {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            return {
                "onboarding_completed": profile.onboarding_completed,
                "onboarding_started_at": profile.onboarding_started_at.isoformat() if profile.onboarding_started_at else None,
                "onboarding_completed_at": profile.onboarding_completed_at.isoformat() if profile.onboarding_completed_at else None,
                "has_interests": bool(profile.interests),
                "has_bio": bool(profile.bio),
                "has_location": bool(profile.location)
            }
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to get onboarding status {user_id}: {e}")
            raise
    
    async def complete_onboarding(self, user_id: str, onboarding_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Mark onboarding as complete and save data
        
        Args:
            user_id: User ID
            onboarding_data: Onboarding completion data
        
        Returns:
            dict: Onboarding completion confirmation
        
        Raises:
            UserNotFoundError: If user not found
        """
        try:
            result = await self.db.execute(
                select(UserProfile).where(UserProfile.user_id == user_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                raise UserNotFoundError(f"User profile {user_id} not found", ErrorCodes.USER_NOT_FOUND)
            
            # Update onboarding status
            profile.onboarding_completed = True
            profile.onboarding_completed_at = datetime.utcnow()
            profile.onboarding_data = onboarding_data
            
            # Extract and save profile data from onboarding
            if "interests" in onboarding_data:
                profile.interests = onboarding_data["interests"]
            
            if "bio" in onboarding_data:
                profile.bio = onboarding_data["bio"]
            
            if "location" in onboarding_data:
                profile.location = onboarding_data["location"]
            
            if "personality_traits" in onboarding_data:
                profile.personality_traits = onboarding_data["personality_traits"]
            
            if "social_preferences" in onboarding_data:
                profile.social_preferences = onboarding_data["social_preferences"]
            
            profile.updated_at = datetime.utcnow()
            await self.db.commit()
            
            logger.info(f"Completed onboarding for user: {user_id}")
            
            return {
                "onboarding_completed": True,
                "completed_at": profile.onboarding_completed_at.isoformat(),
                "message": "Onboarding completed successfully"
            }
            
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to complete onboarding {user_id}: {e}")
            await self.db.rollback()
            raise
