"""
TONI V3 AI Backend - Error Handlers

Global error handlers for FastAPI application.
"""

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
from typing import Union
import traceback

from app.core.exceptions import ToniHT<PERSON><PERSON>x<PERSON>, ToniBaseException, ErrorCodes
from app.core.logging import get_logger

logger = get_logger(__name__)


async def toni_http_exception_handler(
    request: Request, 
    exc: ToniHTTPException
) -> JSONResponse:
    """Handle custom TONI HTTP exceptions"""
    
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.warning(
        f"TONI HTTP exception: {exc.detail}",
        request_id=request_id,
        status_code=exc.status_code,
        error_code=exc.error_code,
        details=exc.details
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail.get("message") if isinstance(exc.detail, dict) else str(exc.detail),
            "error_code": exc.error_code,
            "details": exc.details,
            "request_id": request_id
        },
        headers=exc.headers
    )


async def http_exception_handler(
    request: Request, 
    exc: Union[HTTPException, StarletteHTTPException]
) -> JSONResponse:
    """Handle standard HTTP exceptions"""
    
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.warning(
        f"HTTP exception: {exc.detail}",
        request_id=request_id,
        status_code=exc.status_code
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": str(exc.detail),
            "error_code": f"HTTP_{exc.status_code}",
            "request_id": request_id
        },
        headers=getattr(exc, 'headers', None)
    )


async def validation_exception_handler(
    request: Request, 
    exc: RequestValidationError
) -> JSONResponse:
    """Handle request validation errors"""
    
    request_id = getattr(request.state, "request_id", "unknown")
    
    # Extract validation error details
    errors = []
    for error in exc.errors():
        errors.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    logger.warning(
        f"Validation error: {len(errors)} validation errors",
        request_id=request_id,
        validation_errors=errors
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": True,
            "message": "Validation failed",
            "error_code": ErrorCodes.VALIDATION_ERROR,
            "details": {
                "validation_errors": errors
            },
            "request_id": request_id
        }
    )


async def toni_base_exception_handler(
    request: Request, 
    exc: ToniBaseException
) -> JSONResponse:
    """Handle base TONI exceptions"""
    
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.error(
        f"TONI base exception: {exc.message}",
        request_id=request_id,
        error_code=exc.error_code,
        details=exc.details,
        exc_info=True
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": True,
            "message": exc.message,
            "error_code": exc.error_code or ErrorCodes.INTERNAL_SERVER_ERROR,
            "details": exc.details,
            "request_id": request_id
        }
    )


async def general_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """Handle all other exceptions"""
    
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.error(
        f"Unhandled exception: {str(exc)}",
        request_id=request_id,
        exception_type=type(exc).__name__,
        traceback=traceback.format_exc()
    )
    
    # Don't expose internal error details in production
    from app.core.config import settings
    
    if settings.is_production:
        message = "An internal error occurred"
        details = {}
    else:
        message = str(exc)
        details = {
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc().split('\n')
        }
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": True,
            "message": message,
            "error_code": ErrorCodes.INTERNAL_SERVER_ERROR,
            "details": details,
            "request_id": request_id
        }
    )


# Database-specific error handlers
async def database_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """Handle database-related exceptions"""
    
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.error(
        f"Database exception: {str(exc)}",
        request_id=request_id,
        exception_type=type(exc).__name__
    )
    
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={
            "error": True,
            "message": "Database service temporarily unavailable",
            "error_code": ErrorCodes.DATABASE_ERROR,
            "request_id": request_id
        }
    )


# OpenAI-specific error handlers
async def openai_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """Handle OpenAI API exceptions"""
    
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.error(
        f"OpenAI exception: {str(exc)}",
        request_id=request_id,
        exception_type=type(exc).__name__
    )
    
    # Determine appropriate status code based on OpenAI error
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    error_code = ErrorCodes.OPENAI_ERROR
    
    if "rate limit" in str(exc).lower():
        status_code = status.HTTP_429_TOO_MANY_REQUESTS
        error_code = ErrorCodes.RATE_LIMIT_EXCEEDED
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": True,
            "message": "AI service temporarily unavailable",
            "error_code": error_code,
            "request_id": request_id
        }
    )


def register_error_handlers(app) -> None:
    """Register all error handlers with FastAPI app"""
    
    # Custom TONI exceptions
    app.add_exception_handler(ToniHTTPException, toni_http_exception_handler)
    app.add_exception_handler(ToniBaseException, toni_base_exception_handler)
    
    # Standard HTTP exceptions
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # Validation exceptions
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # Database exceptions (SQLAlchemy)
    try:
        from sqlalchemy.exc import SQLAlchemyError
        app.add_exception_handler(SQLAlchemyError, database_exception_handler)
    except ImportError:
        pass
    
    # OpenAI exceptions
    try:
        from openai import OpenAIError
        app.add_exception_handler(OpenAIError, openai_exception_handler)
    except ImportError:
        pass
    
    # General exception handler (catch-all)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Error handlers registered successfully")
