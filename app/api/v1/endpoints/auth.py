"""
TONI V3 AI Backend - Authentication Endpoints

Endpoints for user authentication, registration, and OAuth flows.
"""

from fastapi import API<PERSON>outer, HTTPException, Depends, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import RedirectResponse
from typing import Dict, Any, Optional
from pydantic import BaseModel, EmailStr
import logging

from app.core.config import settings
from app.core.database import get_db
from app.core.auth import get_current_user_id, google_oauth
from app.services.auth_service import AuthService
from app.core.exceptions import (
    UserAlreadyExistsError,
    AuthenticationError,
    create_authentication_error,
    create_validation_error
)

router = APIRouter()
security = HTTPBearer()
logger = logging.getLogger(__name__)


# Pydantic models for request/response
class UserRegistration(BaseModel):
    email: EmailStr
    name: str
    password: Optional[str] = None


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    user: Dict[str, Any]


class RefreshTokenRequest(BaseModel):
    refresh_token: str


@router.post("/register", response_model=TokenResponse)
async def register_user(
    user_data: UserRegistration,
    db = Depends(get_db)
) -> TokenResponse:
    """
    Register a new user

    Args:
        user_data: User registration data
        db: Database session

    Returns:
        TokenResponse: Registration result with user info and tokens
    """
    try:
        auth_service = AuthService(db)

        # Create user
        user = await auth_service.create_user(
            email=user_data.email,
            name=user_data.name,
            auth_provider="email"
        )

        # Create tokens
        tokens = await auth_service.create_user_tokens(user)

        return TokenResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            user={
                "id": str(user.id),
                "email": user.email,
                "name": user.name,
                "is_verified": user.is_verified
            }
        )

    except UserAlreadyExistsError as e:
        raise create_validation_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=TokenResponse)
async def login_user(
    credentials: UserLogin,
    db = Depends(get_db)
) -> TokenResponse:
    """
    User login with email/password

    Args:
        credentials: Login credentials
        db: Database session

    Returns:
        TokenResponse: Login result with tokens
    """
    try:
        auth_service = AuthService(db)

        # Authenticate user
        user = await auth_service.authenticate_user(
            credentials.email,
            credentials.password
        )

        if not user:
            raise create_authentication_error("Invalid credentials")

        # Create tokens
        tokens = await auth_service.create_user_tokens(user)

        return TokenResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            user={
                "id": str(user.id),
                "email": user.email,
                "name": user.name,
                "is_verified": user.is_verified
            }
        )

    except AuthenticationError as e:
        raise create_authentication_error(str(e))
    except Exception as e:
        logger.error(f"Login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.get("/google")
async def google_oauth_login(
    state: Optional[str] = Query(None)
) -> Dict[str, str]:
    """
    Initiate Google OAuth login

    Args:
        state: State parameter for CSRF protection

    Returns:
        dict: Authorization URL
    """
    try:
        auth_url = google_oauth.get_authorization_url(state)
        return {"authorization_url": auth_url}

    except Exception as e:
        logger.error(f"Google OAuth initiation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth initiation failed"
        )


@router.post("/apple")
async def apple_signin_login(
    # TODO: Add Apple Sign In schema
    apple_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Apple Sign In login
    
    Args:
        apple_data: Apple Sign In response data
    
    Returns:
        dict: Login result with tokens
    """
    # TODO: Implement Apple Sign In logic
    return {
        "message": "Apple Sign In endpoint - implementation pending",
        "apple_data": apple_data
    }


@router.post("/refresh")
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db = Depends(get_db)
) -> Dict[str, str]:
    """
    Refresh access token

    Args:
        refresh_data: Refresh token data
        db: Database session

    Returns:
        dict: New access token
    """
    try:
        auth_service = AuthService(db)

        # Refresh token
        tokens = await auth_service.refresh_access_token(refresh_data.refresh_token)

        return tokens

    except AuthenticationError as e:
        raise create_authentication_error(str(e))
    except Exception as e:
        logger.error(f"Token refresh failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout_user(
    user_id: str = Depends(get_current_user_id)
) -> Dict[str, str]:
    """
    User logout

    Args:
        user_id: Current user ID from token

    Returns:
        dict: Logout confirmation
    """
    # TODO: Implement token blacklisting/invalidation
    # For now, logout is handled client-side by discarding tokens
    logger.info(f"User logged out: {user_id}")

    return {
        "message": "Logged out successfully",
        "detail": "Please discard your tokens on the client side"
    }


@router.get("/me")
async def get_current_user(
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get current authenticated user info

    Args:
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: Current user information
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_user_by_id(user_id)

        if not user:
            raise create_authentication_error("User not found")

        return {
            "id": str(user.id),
            "email": user.email,
            "name": user.name,
            "is_verified": user.is_verified,
            "auth_provider": user.auth_provider,
            "created_at": user.created_at.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None
        }

    except Exception as e:
        logger.error(f"Get current user failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )


@router.get("/google/callback", response_model=TokenResponse)
async def google_oauth_callback(
    code: Optional[str] = Query(None),
    state: Optional[str] = Query(None),
    error: Optional[str] = Query(None),
    db = Depends(get_db)
) -> TokenResponse:
    """
    Google OAuth callback endpoint

    Args:
        code: Authorization code from Google
        state: State parameter for CSRF protection
        error: Error from Google OAuth
        db: Database session

    Returns:
        TokenResponse: OAuth callback result with tokens
    """
    if error:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth error: {error}"
        )

    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Authorization code required"
        )

    try:
        auth_service = AuthService(db)

        # Handle Google OAuth
        user, tokens = await auth_service.handle_google_oauth(code, state)

        return TokenResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            user={
                "id": str(user.id),
                "email": user.email,
                "name": user.name,
                "is_verified": user.is_verified
            }
        )

    except AuthenticationError as e:
        raise create_authentication_error(str(e))
    except Exception as e:
        logger.error(f"Google OAuth callback failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth authentication failed"
        )
