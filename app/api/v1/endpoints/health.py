"""
TONI V3 AI Backend - Health Check Endpoints

Endpoints for monitoring system health and status.
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
import asyncio
import time

from app.core.database import DatabaseHealthCheck
from app.core.redis import redis_manager
from app.core.config import settings

router = APIRouter()


@router.get("/")
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint
    
    Returns:
        dict: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": int(time.time()),
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """
    Detailed health check including all services
    
    Returns:
        dict: Detailed health status of all components
    """
    health_status = {
        "status": "healthy",
        "timestamp": int(time.time()),
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "services": {}
    }
    
    # Check database health
    try:
        db_healthy = await DatabaseHealthCheck.check_connection()
        db_info = await DatabaseHealthCheck.get_connection_info()
        health_status["services"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "details": db_info
        }
    except Exception as e:
        health_status["services"]["database"] = {
            "status": "error",
            "error": str(e)
        }
    
    # Check Redis health
    try:
        redis_healthy = await redis_manager.health_check()
        redis_info = await redis_manager.get_info()
        health_status["services"]["redis"] = {
            "status": "healthy" if redis_healthy else "unhealthy",
            "details": redis_info
        }
    except Exception as e:
        health_status["services"]["redis"] = {
            "status": "error",
            "error": str(e)
        }
    
    # Check OpenAI configuration
    health_status["services"]["openai"] = {
        "status": "configured" if settings.OPENAI_API_KEY else "not_configured",
        "model": settings.OPENAI_CHAT_MODEL,
        "voice_model": settings.OPENAI_VOICE_MODEL
    }
    
    # Determine overall status
    service_statuses = [
        service["status"] for service in health_status["services"].values()
    ]
    
    if "error" in service_statuses or "unhealthy" in service_statuses:
        health_status["status"] = "unhealthy"
    elif "not_configured" in service_statuses:
        health_status["status"] = "degraded"
    
    return health_status


@router.get("/readiness")
async def readiness_check() -> Dict[str, Any]:
    """
    Kubernetes readiness probe endpoint
    
    Returns:
        dict: Readiness status
    
    Raises:
        HTTPException: If service is not ready
    """
    try:
        # Check critical services
        db_healthy = await DatabaseHealthCheck.check_connection()
        redis_healthy = await redis_manager.health_check()
        
        if not db_healthy:
            raise HTTPException(status_code=503, detail="Database not ready")
        
        if not redis_healthy:
            raise HTTPException(status_code=503, detail="Redis not ready")
        
        return {
            "status": "ready",
            "timestamp": int(time.time())
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")


@router.get("/liveness")
async def liveness_check() -> Dict[str, Any]:
    """
    Kubernetes liveness probe endpoint
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": int(time.time()),
        "uptime": time.time() - start_time
    }


# Track application start time
start_time = time.time()
