"""
TONI V3 AI Backend - User Management Endpoints

Endpoints for user profile management and user-related operations.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTTPBearer
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging

from app.core.database import get_db
from app.core.auth import get_current_user_id
from app.services.user_service import UserService
from app.core.exceptions import (
    UserNotFoundError,
    ValidationError,
    create_not_found_error,
    create_validation_error
)

router = APIRouter()
security = HTTPBearer()
logger = logging.getLogger(__name__)


# Pydantic models for request/response
class ProfileUpdate(BaseModel):
    bio: Optional[str] = None
    location: Optional[str] = None
    age_range: Optional[str] = None
    interests: Optional[List[str]] = None
    personality_traits: Optional[Dict[str, Any]] = None
    social_preferences: Optional[Dict[str, Any]] = None
    availability: Optional[Dict[str, Any]] = None
    privacy_settings: Optional[Dict[str, Any]] = None
    introduction_preferences: Optional[Dict[str, Any]] = None


class InterestsUpdate(BaseModel):
    interests: List[str]


class PreferencesUpdate(BaseModel):
    social_preferences: Optional[Dict[str, Any]] = None
    privacy_settings: Optional[Dict[str, Any]] = None
    introduction_preferences: Optional[Dict[str, Any]] = None
    availability: Optional[Dict[str, Any]] = None


class OnboardingCompletion(BaseModel):
    interests: Optional[List[str]] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    personality_traits: Optional[Dict[str, Any]] = None
    social_preferences: Optional[Dict[str, Any]] = None


@router.get("/profile")
async def get_user_profile(
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get current user's profile

    Args:
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: User profile data
    """
    try:
        user_service = UserService(db)
        profile = await user_service.get_user_profile(user_id)
        return profile

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Get user profile failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.put("/profile")
async def update_user_profile(
    profile_data: ProfileUpdate,
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, Any]:
    """
    Update current user's profile

    Args:
        profile_data: Updated profile data
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: Updated profile data
    """
    try:
        user_service = UserService(db)

        # Convert Pydantic model to dict, excluding None values
        update_data = profile_data.dict(exclude_none=True)

        updated_profile = await user_service.update_user_profile(user_id, update_data)
        return updated_profile

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except ValidationError as e:
        raise create_validation_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Update user profile failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )


@router.get("/interests")
async def get_user_interests(
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, List[str]]:
    """
    Get current user's interests

    Args:
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: User interests
    """
    try:
        user_service = UserService(db)
        profile = await user_service.get_user_profile(user_id)

        return {
            "interests": profile["profile"]["interests"] or []
        }

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Get user interests failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user interests"
        )


@router.put("/interests")
async def update_user_interests(
    interests_data: InterestsUpdate,
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, List[str]]:
    """
    Update current user's interests

    Args:
        interests_data: Updated interests data
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: Updated interests
    """
    try:
        user_service = UserService(db)

        updated_interests = await user_service.update_user_interests(
            user_id,
            interests_data.interests
        )

        return {
            "interests": updated_interests
        }

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except ValidationError as e:
        raise create_validation_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Update user interests failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user interests"
        )


@router.get("/preferences")
async def get_user_preferences(
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get current user's preferences and settings

    Args:
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: User preferences
    """
    try:
        user_service = UserService(db)
        preferences = await user_service.get_user_preferences(user_id)
        return preferences

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Get user preferences failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user preferences"
        )


@router.put("/preferences")
async def update_user_preferences(
    preferences_data: PreferencesUpdate,
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, Any]:
    """
    Update current user's preferences and settings

    Args:
        preferences_data: Updated preferences data
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: Updated preferences
    """
    try:
        user_service = UserService(db)

        # Convert Pydantic model to dict, excluding None values
        update_data = preferences_data.dict(exclude_none=True)

        updated_preferences = await user_service.update_user_preferences(user_id, update_data)
        return updated_preferences

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except ValidationError as e:
        raise create_validation_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Update user preferences failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user preferences"
        )


@router.delete("/account")
async def delete_user_account(
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, str]:
    """
    Delete current user's account

    Args:
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: Deletion confirmation
    """
    try:
        from app.services.auth_service import AuthService

        auth_service = AuthService(db)
        success = await auth_service.deactivate_user(user_id)

        if success:
            return {
                "message": "Account deactivated successfully",
                "detail": "Your account has been deactivated and will be permanently deleted after 30 days"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User account not found"
            )

    except Exception as e:
        logger.error(f"Delete user account failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user account"
        )


@router.get("/onboarding-status")
async def get_onboarding_status(
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get current user's onboarding status

    Args:
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: Onboarding status
    """
    try:
        user_service = UserService(db)
        status_data = await user_service.get_onboarding_status(user_id)
        return status_data

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Get onboarding status failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get onboarding status"
        )


@router.post("/complete-onboarding")
async def complete_onboarding(
    onboarding_data: OnboardingCompletion,
    user_id: str = Depends(get_current_user_id),
    db = Depends(get_db)
) -> Dict[str, Any]:
    """
    Mark onboarding as complete and save data

    Args:
        onboarding_data: Onboarding completion data
        user_id: Current user ID from token
        db: Database session

    Returns:
        dict: Onboarding completion confirmation
    """
    try:
        user_service = UserService(db)

        # Convert Pydantic model to dict, excluding None values
        completion_data = onboarding_data.dict(exclude_none=True)

        result = await user_service.complete_onboarding(user_id, completion_data)
        return result

    except UserNotFoundError as e:
        raise create_not_found_error(str(e), e.error_code)
    except ValidationError as e:
        raise create_validation_error(str(e), e.error_code)
    except Exception as e:
        logger.error(f"Complete onboarding failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete onboarding"
        )
