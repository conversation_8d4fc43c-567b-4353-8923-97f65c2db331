"""
TONI V3 AI Backend - API v1 Router

Main router that includes all API endpoints for version 1.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import health, auth, chat, users, events

# Create main API router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)

api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

api_router.include_router(
    chat.router,
    prefix="/chat",
    tags=["chat"]
)

api_router.include_router(
    events.router,
    prefix="/events",
    tags=["events"]
)
