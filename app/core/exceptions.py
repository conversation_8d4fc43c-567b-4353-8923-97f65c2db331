"""
TONI V3 AI Backend - Custom Exceptions

Custom exception classes and error handling utilities.
"""

from typing import Any, Dict, Optional
from fastapi import HTT<PERSON>Exception, status


class ToniBaseException(Exception):
    """Base exception for TONI application"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ToniHTTPException(HTTPException):
    """Custom HTTP exception with additional context"""
    
    def __init__(
        self,
        status_code: int,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        self.error_code = error_code
        self.details = details or {}
        
        detail = {
            "message": message,
            "error_code": error_code,
            "details": self.details
        }
        
        super().__init__(status_code=status_code, detail=detail, headers=headers)


# Authentication Exceptions
class AuthenticationError(ToniBaseException):
    """Authentication related errors"""
    pass


class AuthorizationError(ToniBaseException):
    """Authorization related errors"""
    pass


class InvalidTokenError(AuthenticationError):
    """Invalid or expired token"""
    pass


class UserNotFoundError(ToniBaseException):
    """User not found"""
    pass


class UserAlreadyExistsError(ToniBaseException):
    """User already exists"""
    pass


# Chat and AI Exceptions
class ChatError(ToniBaseException):
    """Chat related errors"""
    pass


class OpenAIError(ToniBaseException):
    """OpenAI API related errors"""
    pass


class RateLimitError(ToniBaseException):
    """Rate limit exceeded"""
    pass


class ContentModerationError(ToniBaseException):
    """Content moderation violation"""
    pass


# Database Exceptions
class DatabaseError(ToniBaseException):
    """Database related errors"""
    pass


class RecordNotFoundError(DatabaseError):
    """Database record not found"""
    pass


class DuplicateRecordError(DatabaseError):
    """Duplicate database record"""
    pass


# Event Exceptions
class EventError(ToniBaseException):
    """Event related errors"""
    pass


class EventNotFoundError(EventError):
    """Event not found"""
    pass


class EventCapacityError(EventError):
    """Event at capacity"""
    pass


# Validation Exceptions
class ValidationError(ToniBaseException):
    """Data validation errors"""
    pass


class ConfigurationError(ToniBaseException):
    """Configuration related errors"""
    pass


# HTTP Exception Factories
def create_authentication_error(
    message: str = "Authentication required",
    error_code: str = "AUTHENTICATION_REQUIRED"
) -> ToniHTTPException:
    """Create authentication HTTP exception"""
    return ToniHTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        message=message,
        error_code=error_code,
        headers={"WWW-Authenticate": "Bearer"}
    )


def create_authorization_error(
    message: str = "Insufficient permissions",
    error_code: str = "INSUFFICIENT_PERMISSIONS"
) -> ToniHTTPException:
    """Create authorization HTTP exception"""
    return ToniHTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        message=message,
        error_code=error_code
    )


def create_not_found_error(
    message: str = "Resource not found",
    error_code: str = "RESOURCE_NOT_FOUND"
) -> ToniHTTPException:
    """Create not found HTTP exception"""
    return ToniHTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        message=message,
        error_code=error_code
    )


def create_validation_error(
    message: str = "Validation failed",
    error_code: str = "VALIDATION_ERROR",
    details: Optional[Dict[str, Any]] = None
) -> ToniHTTPException:
    """Create validation HTTP exception"""
    return ToniHTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message=message,
        error_code=error_code,
        details=details
    )


def create_rate_limit_error(
    message: str = "Rate limit exceeded",
    error_code: str = "RATE_LIMIT_EXCEEDED",
    retry_after: Optional[int] = None
) -> ToniHTTPException:
    """Create rate limit HTTP exception"""
    headers = {}
    if retry_after:
        headers["Retry-After"] = str(retry_after)
    
    return ToniHTTPException(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        message=message,
        error_code=error_code,
        headers=headers
    )


def create_server_error(
    message: str = "Internal server error",
    error_code: str = "INTERNAL_SERVER_ERROR"
) -> ToniHTTPException:
    """Create server error HTTP exception"""
    return ToniHTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message=message,
        error_code=error_code
    )


def create_service_unavailable_error(
    message: str = "Service temporarily unavailable",
    error_code: str = "SERVICE_UNAVAILABLE"
) -> ToniHTTPException:
    """Create service unavailable HTTP exception"""
    return ToniHTTPException(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        message=message,
        error_code=error_code
    )


# Error code constants
class ErrorCodes:
    """Standard error codes"""
    
    # Authentication & Authorization
    AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED"
    INVALID_TOKEN = "INVALID_TOKEN"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    
    # User Management
    USER_NOT_FOUND = "USER_NOT_FOUND"
    USER_ALREADY_EXISTS = "USER_ALREADY_EXISTS"
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    
    # Chat & AI
    CHAT_ERROR = "CHAT_ERROR"
    OPENAI_ERROR = "OPENAI_ERROR"
    CONTENT_MODERATION_VIOLATION = "CONTENT_MODERATION_VIOLATION"
    
    # Events
    EVENT_NOT_FOUND = "EVENT_NOT_FOUND"
    EVENT_CAPACITY_EXCEEDED = "EVENT_CAPACITY_EXCEEDED"
    
    # General
    VALIDATION_ERROR = "VALIDATION_ERROR"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    DATABASE_ERROR = "DATABASE_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
