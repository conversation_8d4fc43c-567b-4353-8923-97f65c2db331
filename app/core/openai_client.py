"""
TONI V3 AI Backend - OpenAI Client Configuration

OpenAI SDK setup and client management for chat, voice, and embedding operations.
"""

import openai
from openai import Async<PERSON>penA<PERSON>
from typing import Dict, Any, List, Optional, AsyncGenerator
import logging
import asyncio
from datetime import datetime
import json

from app.core.config import settings

logger = logging.getLogger(__name__)


class OpenAIManager:
    """OpenAI client and operations manager"""
    
    def __init__(self):
        self._client: Optional[AsyncOpenAI] = None
        self._initialized = False
    
    def initialize(self) -> None:
        """Initialize OpenAI client"""
        try:
            if not settings.OPENAI_API_KEY:
                raise ValueError("OpenAI API key not configured")
            
            # Initialize async client
            self._client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                organization=settings.OPENAI_ORG_ID,
                timeout=settings.OPENAI_TIMEOUT,
                max_retries=settings.OPENAI_MAX_RETRIES,
            )
            
            self._initialized = True
            logger.info("OpenAI client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            raise
    
    @property
    def client(self) -> AsyncOpenAI:
        """Get OpenAI client instance"""
        if not self._initialized or not self._client:
            raise RuntimeError("OpenAI client not initialized. Call initialize() first.")
        return self._client
    
    async def health_check(self) -> Dict[str, Any]:
        """Check OpenAI API connectivity"""
        try:
            # Simple API call to test connectivity
            response = await self.client.models.list()
            models = [model.id for model in response.data]
            
            return {
                "status": "connected",
                "available_models": len(models),
                "chat_model": settings.OPENAI_CHAT_MODEL,
                "voice_model": settings.OPENAI_VOICE_MODEL,
                "embedding_model": settings.OPENAI_EMBEDDING_MODEL,
            }
        except Exception as e:
            logger.error(f"OpenAI health check failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }


# Global OpenAI manager instance
openai_manager = OpenAIManager()


class ChatService:
    """Service for OpenAI chat completions"""
    
    def __init__(self, client: AsyncOpenAI):
        self.client = client
    
    async def create_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        stream: bool = False,
        functions: Optional[List[Dict[str, Any]]] = None,
        function_call: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a chat completion
        
        Args:
            messages: List of message objects
            model: Model to use (defaults to configured chat model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            stream: Whether to stream the response
            functions: Available functions for function calling
            function_call: Function call preference
            **kwargs: Additional parameters
        
        Returns:
            dict: Chat completion response
        """
        try:
            model = model or settings.OPENAI_CHAT_MODEL
            max_tokens = max_tokens or settings.OPENAI_MAX_TOKENS
            
            params = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
                **kwargs
            }
            
            # Add function calling if provided
            if functions:
                params["functions"] = functions
                if function_call:
                    params["function_call"] = function_call
            
            response = await self.client.chat.completions.create(**params)
            
            if stream:
                return response  # Return async generator for streaming
            else:
                return {
                    "content": response.choices[0].message.content,
                    "function_call": getattr(response.choices[0].message, 'function_call', None),
                    "finish_reason": response.choices[0].finish_reason,
                    "usage": response.usage.dict() if response.usage else None,
                    "model": response.model,
                }
                
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise
    
    async def create_streaming_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Create a streaming chat completion
        
        Args:
            messages: List of message objects
            model: Model to use
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional parameters
        
        Yields:
            str: Streaming content chunks
        """
        try:
            model = model or settings.OPENAI_CHAT_MODEL
            max_tokens = max_tokens or settings.OPENAI_MAX_TOKENS
            
            stream = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Streaming completion failed: {e}")
            raise


class EmbeddingService:
    """Service for OpenAI embeddings"""
    
    def __init__(self, client: AsyncOpenAI):
        self.client = client
    
    async def create_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """
        Create an embedding for text
        
        Args:
            text: Text to embed
            model: Embedding model to use
        
        Returns:
            list: Embedding vector
        """
        try:
            model = model or settings.OPENAI_EMBEDDING_MODEL
            
            response = await self.client.embeddings.create(
                model=model,
                input=text
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Embedding creation failed: {e}")
            raise
    
    async def create_embeddings_batch(
        self,
        texts: List[str],
        model: Optional[str] = None
    ) -> List[List[float]]:
        """
        Create embeddings for multiple texts
        
        Args:
            texts: List of texts to embed
            model: Embedding model to use
        
        Returns:
            list: List of embedding vectors
        """
        try:
            model = model or settings.OPENAI_EMBEDDING_MODEL
            
            response = await self.client.embeddings.create(
                model=model,
                input=texts
            )
            
            return [item.embedding for item in response.data]
            
        except Exception as e:
            logger.error(f"Batch embedding creation failed: {e}")
            raise


class ModerationService:
    """Service for OpenAI content moderation"""
    
    def __init__(self, client: AsyncOpenAI):
        self.client = client
    
    async def moderate_content(self, content: str) -> Dict[str, Any]:
        """
        Moderate content for policy violations
        
        Args:
            content: Content to moderate
        
        Returns:
            dict: Moderation results
        """
        try:
            response = await self.client.moderations.create(input=content)
            
            result = response.results[0]
            return {
                "flagged": result.flagged,
                "categories": result.categories.dict(),
                "category_scores": result.category_scores.dict(),
            }
            
        except Exception as e:
            logger.error(f"Content moderation failed: {e}")
            raise


class FunctionCallHandler:
    """Handler for OpenAI function calling"""
    
    def __init__(self):
        self.functions = {}
    
    def register_function(
        self,
        name: str,
        description: str,
        parameters: Dict[str, Any],
        handler: callable
    ) -> None:
        """
        Register a function for OpenAI function calling
        
        Args:
            name: Function name
            description: Function description
            parameters: Function parameters schema
            handler: Function handler callable
        """
        self.functions[name] = {
            "name": name,
            "description": description,
            "parameters": parameters,
            "handler": handler
        }
    
    def get_function_definitions(self) -> List[Dict[str, Any]]:
        """Get function definitions for OpenAI API"""
        return [
            {
                "name": func["name"],
                "description": func["description"],
                "parameters": func["parameters"]
            }
            for func in self.functions.values()
        ]
    
    async def execute_function(
        self,
        function_name: str,
        arguments: Dict[str, Any]
    ) -> Any:
        """
        Execute a registered function
        
        Args:
            function_name: Name of function to execute
            arguments: Function arguments
        
        Returns:
            Function result
        """
        if function_name not in self.functions:
            raise ValueError(f"Function {function_name} not registered")
        
        handler = self.functions[function_name]["handler"]
        
        try:
            if asyncio.iscoroutinefunction(handler):
                return await handler(**arguments)
            else:
                return handler(**arguments)
        except Exception as e:
            logger.error(f"Function execution failed for {function_name}: {e}")
            raise


# Initialize services
def get_chat_service() -> ChatService:
    """Get chat service instance"""
    return ChatService(openai_manager.client)


def get_embedding_service() -> EmbeddingService:
    """Get embedding service instance"""
    return EmbeddingService(openai_manager.client)


def get_moderation_service() -> ModerationService:
    """Get moderation service instance"""
    return ModerationService(openai_manager.client)


def get_function_handler() -> FunctionCallHandler:
    """Get function call handler instance"""
    return FunctionCallHandler()


async def init_openai() -> None:
    """Initialize OpenAI client"""
    openai_manager.initialize()
