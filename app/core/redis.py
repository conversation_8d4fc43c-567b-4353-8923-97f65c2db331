"""
TONI V3 AI Backend - Redis Configuration

Redis setup for caching, session management, and real-time data storage.
"""

import redis.asyncio as redis
import json
import pickle
import time
from typing import Any, Optional, Union, Dict
from datetime import timedelta
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisManager:
    """Redis connection and operation manager"""
    
    def __init__(self):
        self._redis: Optional[redis.Redis] = None
        self._connection_pool: Optional[redis.ConnectionPool] = None
    
    async def connect(self) -> None:
        """Initialize Redis connection"""
        try:
            # Create connection pool
            self._connection_pool = redis.ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=settings.REDIS_POOL_SIZE,
                socket_timeout=settings.REDIS_TIMEOUT,
                socket_connect_timeout=settings.REDIS_TIMEOUT,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            
            # Create Redis client
            self._redis = redis.Redis(
                connection_pool=self._connection_pool,
                decode_responses=True
            )
            
            # Test connection
            await self._redis.ping()
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self) -> None:
        """Close Redis connection"""
        try:
            if self._redis:
                await self._redis.close()
            if self._connection_pool:
                await self._connection_pool.disconnect()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
    
    @property
    def redis(self) -> redis.Redis:
        """Get Redis client instance"""
        if not self._redis:
            raise RuntimeError("Redis not connected. Call connect() first.")
        return self._redis
    
    async def health_check(self) -> bool:
        """Check Redis connection health"""
        try:
            await self.redis.ping()
            return True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False
    
    async def get_info(self) -> Dict[str, Any]:
        """Get Redis server information"""
        try:
            info = await self.redis.info()
            return {
                "status": "connected",
                "version": info.get("redis_version"),
                "memory_used": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed"),
            }
        except Exception as e:
            logger.error(f"Failed to get Redis info: {e}")
            return {"status": "error", "error": str(e)}


# Global Redis manager instance
redis_manager = RedisManager()


async def get_redis() -> redis.Redis:
    """Dependency to get Redis client"""
    return redis_manager.redis


class CacheManager:
    """High-level cache operations manager"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
    
    async def set(
        self,
        key: str,
        value: Any,
        expire: Optional[Union[int, timedelta]] = None,
        serialize: bool = True
    ) -> bool:
        """
        Set a value in cache
        
        Args:
            key: Cache key
            value: Value to cache
            expire: Expiration time in seconds or timedelta
            serialize: Whether to serialize the value as JSON
        
        Returns:
            bool: True if successful
        """
        try:
            if serialize:
                if isinstance(value, (dict, list, tuple)):
                    value = json.dumps(value)
                elif not isinstance(value, (str, int, float, bool)):
                    value = pickle.dumps(value)
            
            if isinstance(expire, timedelta):
                expire = int(expire.total_seconds())
            
            await self.redis.set(key, value, ex=expire)
            return True
            
        except Exception as e:
            logger.error(f"Failed to set cache key {key}: {e}")
            return False
    
    async def get(
        self,
        key: str,
        deserialize: bool = True,
        default: Any = None
    ) -> Any:
        """
        Get a value from cache
        
        Args:
            key: Cache key
            deserialize: Whether to deserialize JSON values
            default: Default value if key not found
        
        Returns:
            Cached value or default
        """
        try:
            value = await self.redis.get(key)
            if value is None:
                return default
            
            if deserialize and isinstance(value, str):
                try:
                    # Try JSON first
                    return json.loads(value)
                except json.JSONDecodeError:
                    try:
                        # Try pickle
                        return pickle.loads(value.encode())
                    except:
                        # Return as string
                        return value
            
            return value
            
        except Exception as e:
            logger.error(f"Failed to get cache key {key}: {e}")
            return default
    
    async def delete(self, key: str) -> bool:
        """Delete a key from cache"""
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Failed to check cache key {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for a key"""
        try:
            return await self.redis.expire(key, seconds)
        except Exception as e:
            logger.error(f"Failed to set expiration for key {key}: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment a numeric value"""
        try:
            return await self.redis.incrby(key, amount)
        except Exception as e:
            logger.error(f"Failed to increment key {key}: {e}")
            return None
    
    async def set_hash(self, key: str, mapping: Dict[str, Any]) -> bool:
        """Set multiple fields in a hash"""
        try:
            # Serialize complex values
            serialized_mapping = {}
            for field, value in mapping.items():
                if isinstance(value, (dict, list, tuple)):
                    serialized_mapping[field] = json.dumps(value)
                else:
                    serialized_mapping[field] = str(value)
            
            await self.redis.hset(key, mapping=serialized_mapping)
            return True
        except Exception as e:
            logger.error(f"Failed to set hash {key}: {e}")
            return False
    
    async def get_hash(self, key: str) -> Dict[str, Any]:
        """Get all fields from a hash"""
        try:
            hash_data = await self.redis.hgetall(key)
            
            # Try to deserialize values
            result = {}
            for field, value in hash_data.items():
                try:
                    result[field] = json.loads(value)
                except json.JSONDecodeError:
                    result[field] = value
            
            return result
        except Exception as e:
            logger.error(f"Failed to get hash {key}: {e}")
            return {}


class SessionManager:
    """User session management using Redis"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.session_prefix = "session:"
        self.user_sessions_prefix = "user_sessions:"
        self.default_expire = 3600  # 1 hour
    
    async def create_session(
        self,
        session_id: str,
        user_id: str,
        data: Dict[str, Any],
        expire: Optional[int] = None
    ) -> bool:
        """Create a new user session"""
        expire = expire or self.default_expire
        session_key = f"{self.session_prefix}{session_id}"
        user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
        
        session_data = {
            "user_id": user_id,
            "created_at": str(int(time.time())),
            **data
        }
        
        # Set session data
        success = await self.cache.set(session_key, session_data, expire=expire)
        
        if success:
            # Add session to user's session list
            await self.cache.redis.sadd(user_sessions_key, session_id)
            await self.cache.expire(user_sessions_key, expire)
        
        return success
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        session_key = f"{self.session_prefix}{session_id}"
        return await self.cache.get(session_key)
    
    async def update_session(
        self,
        session_id: str,
        data: Dict[str, Any]
    ) -> bool:
        """Update session data"""
        session_key = f"{self.session_prefix}{session_id}"
        current_data = await self.get_session(session_id)
        
        if current_data:
            current_data.update(data)
            return await self.cache.set(session_key, current_data)
        
        return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        session_key = f"{self.session_prefix}{session_id}"
        session_data = await self.get_session(session_id)
        
        if session_data:
            user_id = session_data.get("user_id")
            if user_id:
                user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
                await self.cache.redis.srem(user_sessions_key, session_id)
        
        return await self.cache.delete(session_key)
    
    async def get_user_sessions(self, user_id: str) -> list:
        """Get all sessions for a user"""
        user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
        try:
            session_ids = await self.cache.redis.smembers(user_sessions_key)
            return list(session_ids)
        except Exception as e:
            logger.error(f"Failed to get user sessions for {user_id}: {e}")
            return []


# Initialize managers
async def init_redis() -> None:
    """Initialize Redis connection"""
    await redis_manager.connect()


async def close_redis() -> None:
    """Close Redis connection"""
    await redis_manager.disconnect()


def get_cache_manager() -> CacheManager:
    """Get cache manager instance"""
    return CacheManager(redis_manager.redis)


def get_session_manager() -> SessionManager:
    """Get session manager instance"""
    cache_manager = get_cache_manager()
    return SessionManager(cache_manager)
