"""
TONI V3 AI Backend - Logging Configuration

Structured logging setup with JSON formatting and multiple handlers.
"""

import logging
import logging.config
import sys
import json
from datetime import datetime
from typing import Dict, Any, Optional
import traceback
import os

from app.core.config import settings


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        
        # Base log data
        log_data = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add request ID if available
        if hasattr(record, 'request_id'):
            log_data["request_id"] = record.request_id
        
        # Add user ID if available
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id
        
        # Add extra fields
        if hasattr(record, 'extra'):
            log_data.update(record.extra)
        
        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add stack info if present
        if record.stack_info:
            log_data["stack_info"] = record.stack_info
        
        return json.dumps(log_data, default=str)


class ContextFilter(logging.Filter):
    """Filter to add context information to log records"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add context information to log record"""
        
        # Add environment info
        record.environment = settings.ENVIRONMENT
        
        # Add process info
        record.process_id = os.getpid()
        
        return True


def setup_logging() -> None:
    """Setup application logging configuration"""
    
    # Determine log level
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    
    # Create formatters
    if settings.LOG_FORMAT.lower() == "json":
        formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(ContextFilter())
    
    # Create file handler for errors (if not in development)
    handlers = [console_handler]
    
    if not settings.is_development:
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)
        
        file_handler = logging.FileHandler("logs/app.log")
        file_handler.setLevel(logging.WARNING)
        file_handler.setFormatter(formatter)
        file_handler.addFilter(ContextFilter())
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        force=True
    )
    
    # Configure specific loggers
    configure_loggers()
    
    # Setup Sentry if configured
    if settings.SENTRY_DSN:
        setup_sentry()
    
    logging.info("Logging configuration completed")


def configure_loggers() -> None:
    """Configure specific logger levels"""
    
    # Set third-party library log levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    logging.getLogger("redis").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.INFO)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    
    # Set application logger levels
    logging.getLogger("app").setLevel(logging.DEBUG if settings.DEBUG else logging.INFO)


def setup_sentry() -> None:
    """Setup Sentry error tracking"""
    try:
        import sentry_sdk
        from sentry_sdk.integrations.fastapi import FastApiIntegration
        from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
        from sentry_sdk.integrations.redis import RedisIntegration
        from sentry_sdk.integrations.logging import LoggingIntegration
        
        # Configure Sentry integrations
        integrations = [
            FastApiIntegration(auto_enabling_integrations=False),
            SqlalchemyIntegration(),
            RedisIntegration(),
            LoggingIntegration(
                level=logging.INFO,        # Capture info and above as breadcrumbs
                event_level=logging.ERROR  # Send errors as events
            ),
        ]
        
        sentry_sdk.init(
            dsn=settings.SENTRY_DSN,
            environment=settings.ENVIRONMENT,
            integrations=integrations,
            traces_sample_rate=0.1 if settings.is_production else 1.0,
            send_default_pii=False,  # Don't send personally identifiable information
            attach_stacktrace=True,
            before_send=filter_sensitive_data,
        )
        
        logging.info("Sentry error tracking initialized")
        
    except ImportError:
        logging.warning("Sentry SDK not installed, skipping error tracking setup")
    except Exception as e:
        logging.error(f"Failed to setup Sentry: {e}")


def filter_sensitive_data(event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Filter sensitive data from Sentry events"""
    
    # Remove sensitive headers
    if "request" in event and "headers" in event["request"]:
        headers = event["request"]["headers"]
        sensitive_headers = ["authorization", "cookie", "x-api-key"]
        
        for header in sensitive_headers:
            if header in headers:
                headers[header] = "[Filtered]"
    
    # Remove sensitive query parameters
    if "request" in event and "query_string" in event["request"]:
        # Filter out sensitive query parameters
        query_string = event["request"]["query_string"]
        if any(param in query_string.lower() for param in ["token", "key", "secret"]):
            event["request"]["query_string"] = "[Filtered]"
    
    return event


class AppLogger:
    """Application-specific logger wrapper"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message with context"""
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message with context"""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message with context"""
        self._log(logging.ERROR, message, **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message with context"""
        self._log(logging.DEBUG, message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message with context"""
        self._log(logging.CRITICAL, message, **kwargs)
    
    def _log(self, level: int, message: str, **kwargs) -> None:
        """Internal logging method with context"""
        extra = {}
        
        # Extract known context fields
        if "request_id" in kwargs:
            extra["request_id"] = kwargs.pop("request_id")
        
        if "user_id" in kwargs:
            extra["user_id"] = kwargs.pop("user_id")
        
        # Add remaining kwargs as extra data
        if kwargs:
            extra["extra"] = kwargs
        
        self.logger.log(level, message, extra=extra)


def get_logger(name: str) -> AppLogger:
    """Get application logger instance"""
    return AppLogger(name)


# Performance monitoring
class PerformanceMonitor:
    """Simple performance monitoring"""
    
    def __init__(self, logger: AppLogger):
        self.logger = logger
        self.metrics = {}
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a performance metric"""
        self.logger.info(
            f"Metric recorded: {name}",
            metric_name=name,
            metric_value=value,
            metric_tags=tags or {}
        )
    
    def record_timing(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a timing metric"""
        self.record_metric(f"{name}_duration", duration, tags)
    
    def record_counter(self, name: str, count: int = 1, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a counter metric"""
        self.record_metric(f"{name}_count", count, tags)


def get_performance_monitor() -> PerformanceMonitor:
    """Get performance monitor instance"""
    logger = get_logger("performance")
    return PerformanceMonitor(logger)
