"""
TONI V3 AI Backend - Chat Models

Database models for chat conversations and messages.
"""

from sqlalchemy import Column, String, Text, DateTime, JSON, Boolean, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class Conversation(Base):
    """Conversation model for chat sessions"""

    __tablename__ = "conversations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Conversation type
    conversation_type = Column(String(50), nullable=False, index=True)  # onboarding, chat, introduction, group

    # Participants
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    participants = Column(JSON, default=list)  # List of participant user IDs for group chats

    # Conversation metadata
    title = Column(String(255), nullable=True)
    context = Column(JSON, default=dict)  # Conversation context and metadata
    summary = Column(Text, nullable=True)  # AI-generated conversation summary

    # Status
    is_active = Column(Boolean, default=True, index=True)
    is_archived = Column(Boolean, default=False)

    # Message count for optimization
    message_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_message_at = Column(DateTime(timezone=True), nullable=True, index=True)

    # Relationships
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan", order_by="Message.created_at")

    # Table indexes
    __table_args__ = (
        Index('idx_conversation_user_type', 'user_id', 'conversation_type'),
        Index('idx_conversation_active_updated', 'is_active', 'updated_at'),
        Index('idx_conversation_last_message', 'last_message_at'),
    )

    def __repr__(self):
        return f"<Conversation(id={self.id}, type={self.conversation_type}, user_id={self.user_id})>"


class Message(Base):
    """Message model for individual chat messages"""

    __tablename__ = "messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False, index=True)

    # Message content
    content = Column(Text, nullable=False)
    message_type = Column(String(50), default="text", index=True)  # text, audio, system, function_call

    # Sender information
    sender_type = Column(String(20), nullable=False, index=True)  # user, tony, system
    sender_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True)

    # Message metadata
    metadata = Column(JSON, default=dict)  # Additional message data

    # AI-specific fields
    model_used = Column(String(100), nullable=True)  # OpenAI model used
    tokens_used = Column(Integer, nullable=True)  # Token count
    function_call = Column(JSON, nullable=True)  # Function call data if applicable
    response_time = Column(Integer, nullable=True)  # Response time in milliseconds

    # Message status
    is_edited = Column(Boolean, default=False)
    is_deleted = Column(Boolean, default=False, index=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    sender_user = relationship("User", back_populates="messages")

    # Table indexes
    __table_args__ = (
        Index('idx_message_conversation_created', 'conversation_id', 'created_at'),
        Index('idx_message_sender_type_created', 'sender_type', 'created_at'),
        Index('idx_message_sender_user_created', 'sender_id', 'created_at'),
        Index('idx_message_type_created', 'message_type', 'created_at'),
    )

    def __repr__(self):
        return f"<Message(id={self.id}, conversation_id={self.conversation_id}, sender_type={self.sender_type})>"


class Introduction(Base):
    """Introduction model for Tony's user introductions"""

    __tablename__ = "introductions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Users being introduced
    user_a_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    user_b_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)

    # Introduction context
    event_id = Column(UUID(as_uuid=True), ForeignKey("events.id", ondelete="SET NULL"), nullable=True, index=True)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id", ondelete="SET NULL"), nullable=True)

    # Introduction details
    introduction_reason = Column(Text, nullable=True)  # Why they were matched
    common_interests = Column(JSON, default=list)  # Shared interests
    introduction_message = Column(Text, nullable=True)  # Tony's introduction message

    # Matching score and algorithm info
    match_score = Column(Integer, nullable=True)  # Matching algorithm score
    algorithm_version = Column(String(50), nullable=True)  # Version of matching algorithm used

    # Status tracking
    status = Column(String(50), default="pending", index=True)  # pending, accepted, declined, expired, successful

    # Response tracking
    user_a_response = Column(String(20), nullable=True)  # accepted, declined, no_response
    user_b_response = Column(String(20), nullable=True)  # accepted, declined, no_response
    user_a_responded_at = Column(DateTime(timezone=True), nullable=True)
    user_b_responded_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True, index=True)

    # Relationships
    user_a = relationship("User", foreign_keys=[user_a_id])
    user_b = relationship("User", foreign_keys=[user_b_id])
    event = relationship("Event", back_populates="introductions")
    conversation = relationship("Conversation")

    # Table indexes
    __table_args__ = (
        Index('idx_introduction_users', 'user_a_id', 'user_b_id'),
        Index('idx_introduction_status_created', 'status', 'created_at'),
        Index('idx_introduction_event_status', 'event_id', 'status'),
        Index('idx_introduction_expires', 'expires_at'),
    )

    def __repr__(self):
        return f"<Introduction(id={self.id}, user_a_id={self.user_a_id}, user_b_id={self.user_b_id}, status={self.status})>"
