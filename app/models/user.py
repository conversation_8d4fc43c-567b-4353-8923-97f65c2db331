"""
TONI V3 AI Backend - User Models

Database models for user management and authentication.
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class User(Base):
    """User model for authentication and basic profile"""

    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    name = Column(String(255), nullable=False)
    auth_provider = Column(String(50), nullable=False, index=True)  # google, apple, email
    auth_provider_id = Column(String(255), nullable=True, index=True)  # External provider ID
    is_active = Column(Boolean, default=True, index=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    conversations = relationship("Conversation", back_populates="user", cascade="all, delete-orphan")
    messages = relationship("Message", back_populates="sender_user", cascade="all, delete-orphan")
    event_interests = relationship("UserEventInterest", back_populates="user", cascade="all, delete-orphan")
    created_events = relationship("Event", back_populates="creator", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, name={self.name})>"


class UserProfile(Base):
    """Extended user profile with interests and preferences"""

    __tablename__ = "user_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True, index=True)

    # Profile information
    bio = Column(Text, nullable=True)
    location = Column(String(255), nullable=True, index=True)
    age_range = Column(String(20), nullable=True)  # e.g., "25-30"

    # Interests and preferences (JSON fields)
    interests = Column(JSON, default=list)  # List of interest strings
    personality_traits = Column(JSON, default=dict)  # Personality assessment results
    social_preferences = Column(JSON, default=dict)  # Social activity preferences
    availability = Column(JSON, default=dict)  # General availability patterns

    # Onboarding status
    onboarding_completed = Column(Boolean, default=False, index=True)
    onboarding_data = Column(JSON, default=dict)  # Raw onboarding responses
    onboarding_started_at = Column(DateTime(timezone=True), nullable=True)
    onboarding_completed_at = Column(DateTime(timezone=True), nullable=True)

    # Privacy settings
    privacy_settings = Column(JSON, default=dict)
    introduction_preferences = Column(JSON, default=dict)  # Preferences for introductions

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="profile")

    def __repr__(self):
        return f"<UserProfile(id={self.id}, user_id={self.user_id}, onboarding_completed={self.onboarding_completed})>"
