"""
TONI V3 AI Backend - Event Models

Database models for events and user event interactions.
"""

from sqlalchemy import Column, String, Text, DateTime, JSON, Boolean, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class Event(Base):
    """Event model for community events and activities"""

    __tablename__ = "events"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Basic event information
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    location = Column(String(500), nullable=True, index=True)

    # Event timing
    start_datetime = Column(DateTime(timezone=True), nullable=False, index=True)
    end_datetime = Column(DateTime(timezone=True), nullable=True)

    # Event metadata
    category = Column(String(100), nullable=True, index=True)  # sports, social, cultural, etc.
    tags = Column(JSON, default=list)  # List of tag strings
    capacity = Column(Integer, nullable=True)  # Maximum participants
    current_participants = Column(Integer, default=0)  # Current participant count

    # Event type and source
    event_type = Column(String(50), default="community", index=True)  # community, user_created, imported
    source = Column(String(100), nullable=True)  # eventbrite, meetup, manual, etc.
    external_id = Column(String(255), nullable=True, index=True)  # ID from external source

    # Creator information
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True)

    # Event status
    is_active = Column(Boolean, default=True, index=True)
    is_featured = Column(Boolean, default=False, index=True)

    # Additional data
    metadata = Column(JSON, default=dict)  # Additional event data

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    creator = relationship("User", back_populates="created_events")
    user_interests = relationship("UserEventInterest", back_populates="event", cascade="all, delete-orphan")
    introductions = relationship("Introduction", back_populates="event")

    def __repr__(self):
        return f"<Event(id={self.id}, title={self.title}, start_datetime={self.start_datetime})>"


class UserEventInterest(Base):
    """User interest in events"""

    __tablename__ = "user_event_interests"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    event_id = Column(UUID(as_uuid=True), ForeignKey("events.id", ondelete="CASCADE"), nullable=False, index=True)

    # Interest level
    interest_level = Column(String(20), default="interested", index=True)  # interested, going, maybe, not_interested

    # Additional preferences
    preferences = Column(JSON, default=dict)  # User-specific event preferences
    notes = Column(Text, nullable=True)  # User notes about the event

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="event_interests")
    event = relationship("Event", back_populates="user_interests")

    # Unique constraint to prevent duplicate interests
    __table_args__ = (
        Index('idx_user_event_unique', 'user_id', 'event_id', unique=True),
        Index('idx_user_event_interest_level', 'user_id', 'interest_level'),
        Index('idx_event_interest_level', 'event_id', 'interest_level'),
    )

    def __repr__(self):
        return f"<UserEventInterest(user_id={self.user_id}, event_id={self.event_id}, interest_level={self.interest_level})>"
