-- TONI V3 AI Backend - Database Initialization Script
-- This script sets up the initial database structure

-- Create development database if it doesn't exist
SELECT 'CREATE DATABASE toni_v3_dev'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'toni_v3_dev')\gexec

-- Create test database if it doesn't exist
SELECT 'CREATE DATABASE toni_v3_test'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'toni_v3_test')\gexec

-- Connect to the development database
\c toni_v3_dev;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgvector extension for vector similarity search (if available)
-- This is optional and will be used for semantic memory
CREATE EXTENSION IF NOT EXISTS vector;

-- Create initial schema comment
COMMENT ON DATABASE toni_v3_dev IS 'TONI V3 AI Backend - Development Database';

-- Connect to the test database and set it up similarly
\c toni_v3_test;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

COMMENT ON DATABASE toni_v3_test IS 'TONI V3 AI Backend - Test Database';
