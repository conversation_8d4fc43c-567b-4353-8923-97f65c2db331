# TONI V3 AI Backend Dependencies

# Core FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database and ORM
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Redis for caching and sessions
redis==5.0.1
aioredis==2.0.1

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# OpenAI Integration
openai==1.3.7

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Environment and Configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Logging and Monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Production
gunicorn==21.2.0
