# TONI V3 AI Backend - Development Docker Compose
# This file sets up local development services

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: toni_v3_postgres
    environment:
      POSTGRES_DB: toni_v3_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - toni_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: toni_v3_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - toni_network

  # TONI V3 Backend (for development)
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: toni_v3_backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/toni_v3_dev
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - .:/app
      - /app/__pycache__
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - toni_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  toni_network:
    driver: bridge
