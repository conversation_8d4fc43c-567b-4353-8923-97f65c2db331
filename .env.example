# TONI V3 AI Backend - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production
ALLOWED_HOSTS=["localhost", "127.0.0.1", "0.0.0.0"]

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database URL
# Format: postgresql+asyncpg://username:password@host:port/database
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/toni_v3_dev

# Database connection pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis URL for caching and session management
# Format: redis://username:password@host:port/database
REDIS_URL=redis://localhost:6379/0

# Redis connection settings
REDIS_POOL_SIZE=10
REDIS_TIMEOUT=5

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
# OpenAI API Key (required)
OPENAI_API_KEY=sk-your-openai-api-key-here

# OpenAI Organization ID (optional)
OPENAI_ORG_ID=

# Model configurations
OPENAI_CHAT_MODEL=gpt-4
OPENAI_VOICE_MODEL=gpt-4o-realtime-preview
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# API limits and timeouts
OPENAI_MAX_TOKENS=4000
OPENAI_TIMEOUT=30
OPENAI_MAX_RETRIES=3

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT Settings
JWT_SECRET_KEY=your-jwt-secret-key-change-this
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# Apple Sign In Configuration
APPLE_CLIENT_ID=your-apple-client-id
APPLE_TEAM_ID=your-apple-team-id
APPLE_KEY_ID=your-apple-key-id
APPLE_PRIVATE_KEY_PATH=path/to/apple/private/key.p8

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Google Calendar API
GOOGLE_CALENDAR_CREDENTIALS_PATH=path/to/google/credentials.json

# Push Notifications (Firebase)
FIREBASE_CREDENTIALS_PATH=path/to/firebase/credentials.json
FIREBASE_PROJECT_ID=your-firebase-project-id

# Email Service (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Sentry for error tracking
SENTRY_DSN=your-sentry-dsn-url

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log format (json, text)
LOG_FORMAT=json

# =============================================================================
# RATE LIMITING
# =============================================================================
# API rate limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# OpenAI usage limits
OPENAI_DAILY_LIMIT=1000
OPENAI_USER_LIMIT_PER_HOUR=50

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features
ENABLE_VOICE_CHAT=true
ENABLE_CALENDAR_INTEGRATION=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=false

# Admin features
ENABLE_ADMIN_DASHBOARD=true
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Development-specific settings (only used when ENVIRONMENT=development)
DEV_AUTO_RELOAD=true
DEV_SHOW_DOCS=true
DEV_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Testing database (used for tests)
TEST_DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/toni_v3_test
